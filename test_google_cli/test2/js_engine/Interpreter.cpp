#include "Interpreter.h"
#include <iostream>
#include <stdexcept>

Interpreter::Interpreter()
    : tokenizer_(nullptr), returned_(false), initialized_(false),
      max_call_stack_depth_(1000), current_recursion_depth_(0) {

    try {
        // Initialize logging and error handling
        logger_ = utilities::Logger::getLogger("Interpreter");
        error_handler_ = std::make_shared<utilities::ErrorHandler>(logger_);

        call_stack_.push_back({}); // Global scope
        initialized_ = true;

        logger_->debug("Interpreter initialized successfully", "Interpreter", "constructor");
        log_interpreter_state("initialization");

    } catch (const std::exception& e) {
        handle_interpreter_error("Failed to initialize interpreter", e.what());
        initialized_ = false;
    }
}

void Interpreter::eat(TokenType type) {
    try {
        validate_tokenizer_state();

        if (current_token_.type == type) {
            logger_->debug("Consuming token", "Interpreter", "eat", static_cast<int>(type));
            current_token_ = tokenizer_->get_next_token();
        } else {
            std::string error_msg = "Syntax Error: Expected token type " + std::to_string(static_cast<int>(type)) +
                                  ", got " + current_token_.to_string();
            handle_interpreter_error(error_msg, "Token mismatch");
            throw std::runtime_error(error_msg);
        }
    } catch (const std::exception& e) {
        handle_interpreter_error("Failed to consume token", e.what());
        throw;
    }
}

Value& Interpreter::get_variable(const std::string& name) {
    // Search from current scope up to global scope
    for (auto it = call_stack_.rbegin(); it != call_stack_.rend(); ++it) {
        if (it->count(name)) {
            return it->at(name);
        }
    }
    throw std::runtime_error("Runtime Error: Undefined variable '" + name + "'");
}

bool Interpreter::has_variable(const std::string& name) const {
    for (auto it = call_stack_.rbegin(); it != call_stack_.rend(); ++it) {
        if (it->count(name)) {
            return true;
        }
    }
    return false;
}

void Interpreter::set_variable(const std::string& name, const Value& value) {
    // Assign to existing variable in any scope, or create in current scope
    for (auto it = call_stack_.rbegin(); it != call_stack_.rend(); ++it) {
        if (it->count(name)) {
            it->at(name) = value;
            return;
        }
    }
    // If not found in any parent scope, declare in current scope
    call_stack_.back()[name] = value;
}

Value Interpreter::parse_factor() {
    Token token = current_token_;
    if (token.type == MINUS) { // Unary minus
        eat(MINUS);
        Value operand = parse_factor();
        if (operand.type != INT_TYPE) {
            throw std::runtime_error("Type Error: Cannot apply unary minus to non-integer type");
        }
        return Value(-operand.as_int());
    } else if (token.type == NUMBER) {
        eat(NUMBER);
        return Value(std::stoi(token.value));
    } else if (token.type == STRING_LITERAL) {
        eat(STRING_LITERAL);
        return Value(token.value);
    } else if (token.type == IDENTIFIER) {
        std::string identifier_name = token.value;
        eat(IDENTIFIER);
        if (current_token_.type == LPAREN) { // Function call
            return parse_function_call(identifier_name);
        } else {
            // Variable access
            return get_variable(identifier_name);
        }
    } else if (token.type == KEYWORD_TRUE) {
        eat(KEYWORD_TRUE);
        return Value(true);
    } else if (token.type == KEYWORD_FALSE) {
        eat(KEYWORD_FALSE);
        return Value(false);
    } else if (token.type == NOT) {
        eat(NOT);
        Value operand = parse_factor();
        return Value(!operand.as_int()); // Logical NOT on integer representation
    } else if (token.type == LPAREN) {
        eat(LPAREN);
        Value result = parse_boolean_expression();
        eat(RPAREN);
        return result;
    } else {
        throw std::runtime_error("Syntax Error: Unexpected token in factor: " + token.to_string());
    }
}

Value Interpreter::parse_term() {
    Value result = parse_factor();

    while (current_token_.type == MULTIPLY || current_token_.type == DIVIDE) {
        if (result.type != INT_TYPE) {
            throw std::runtime_error("Type Error: Cannot perform arithmetic on non-integer type");
        }
        Token op_token = current_token_;
        if (op_token.type == MULTIPLY) {
            eat(MULTIPLY);
            Value right = parse_factor();
            if (right.type != INT_TYPE) {
                throw std::runtime_error("Type Error: Cannot perform arithmetic on non-integer type");
            }
            result = Value(result.as_int() * right.as_int());
        } else if (op_token.type == DIVIDE) {
            eat(DIVIDE);
            Value right = parse_factor();
            if (right.type != INT_TYPE) {
                throw std::runtime_error("Type Error: Cannot perform arithmetic on non-integer type");
            }
            if (right.as_int() == 0) {
                throw std::runtime_error("Runtime Error: Division by zero");
            }
            result = Value(result.as_int() / right.as_int());
        }
    }
    return result;
}

Value Interpreter::parse_expression() {
    Value result = parse_term();

    while (current_token_.type == PLUS || current_token_.type == MINUS) {
        Token op_token = current_token_;
        if (op_token.type == PLUS) {
            eat(PLUS);
            Value right = parse_term();
            if (result.type == STRING_TYPE || right.type == STRING_TYPE) {
                // String concatenation
                result = Value(result.as_string() + right.as_string());
            } else if (result.type == INT_TYPE && right.type == INT_TYPE) {
                // Integer addition
                result = Value(result.as_int() + right.as_int());
            } else {
                throw std::runtime_error("Type Error: Incompatible types for + operation");
            }
        } else if (op_token.type == MINUS) {
            eat(MINUS);
            Value right = parse_term();
            if (result.type != INT_TYPE || right.type != INT_TYPE) {
                throw std::runtime_error("Type Error: Cannot perform subtraction on non-integer type");
            }
            result = Value(result.as_int() - right.as_int());
        }
    }
    return result;
}

Value Interpreter::parse_boolean_expression() {
    Value result = parse_expression();



    // Handle comparison operators
    while (current_token_.type == EQ_EQ || current_token_.type == NEQ ||
           current_token_.type == LT || current_token_.type == GT ||
           current_token_.type == LTE || current_token_.type == GTE) {
        TokenType op_type = current_token_.type;

        eat(op_type);
        Value right_val = parse_expression();

        // Perform comparison based on integer representation
        int left = result.as_int();
        int right = right_val.as_int();

        switch (op_type) {
            case EQ_EQ:  result = Value(left == right); break;
            case NEQ:    result = Value(left != right); break;
            case LT:     result = Value(left < right); break;
            case GT:     result = Value(left > right); break;
            case LTE:    result = Value(left <= right); break;
            case GTE:    result = Value(left >= right); break;
            default: throw std::runtime_error("Syntax Error: Unknown comparison operator");
        }
    }

    // Handle logical AND and OR
    while (current_token_.type == AND_AND || current_token_.type == OR_OR) {
        TokenType op_type = current_token_.type;

        eat(op_type);
        Value right_val = parse_expression(); // Changed from parse_boolean_expression to avoid infinite recursion

        int left = result.as_int();
        int right = right_val.as_int();

        switch (op_type) {
            case AND_AND: result = Value(left && right); break;
            case OR_OR:   result = Value(left || right); break;
            default: throw std::runtime_error("Syntax Error: Unknown logical operator");
        }
        std::cout << "[DEBUG] Logical operation result: " << result.as_string() << std::endl;
    }

    std::cout << "[DEBUG] parse_boolean_expression completed, result: " << result.as_string() << ", current token: " << current_token_.to_string() << std::endl;
    return result;
}

void Interpreter::handle_print_statement() {
    eat(KEYWORD_PRINT);
    eat(LPAREN);
    Value value = parse_boolean_expression(); // Can print result of any expression including boolean
    eat(RPAREN);
    print_callback_(value.as_string() + "\n");
}

void Interpreter::parse_block() {
    eat(LBRACE);
    while (current_token_.type != RBRACE && current_token_.type != END_OF_FILE && !returned_) {
        parse_statement();
    }
    eat(RBRACE);
}

void Interpreter::handle_if_statement() {
    eat(KEYWORD_IF);
    eat(LPAREN);
    Value condition_val = parse_boolean_expression();
    int condition = condition_val.as_int();
    eat(RPAREN);

    if (condition) {
        parse_block(); // Execute the 'if' block
        if (current_token_.type == KEYWORD_ELSE) {
            eat(KEYWORD_ELSE);
            consume_block(); // Skip the 'else' block
        }
    } else {
        consume_block(); // Skip the 'if' block
        if (current_token_.type == KEYWORD_ELSE) {
            eat(KEYWORD_ELSE);
            parse_block(); // Execute the 'else' block
        }
    }
}

void Interpreter::handle_while_statement() {
    std::cout << "[DEBUG] handle_while_statement() started" << std::endl;
    eat(KEYWORD_WHILE);
    eat(LPAREN);

    // Store the condition expression position BEFORE parsing it
    // We need to go back one position to capture the current token position
    size_t condition_start_pos = tokenizer_->get_current_pos() - 1;
    std::cout << "[DEBUG] Captured condition_start_pos: " << condition_start_pos << ", current token: " << current_token_.to_string() << std::endl;
    Value condition_val = parse_boolean_expression();
    int condition = condition_val.as_int();
    std::cout << "[DEBUG] Initial condition: " << condition << std::endl;
    eat(RPAREN);

    // Store the block start position (should point to the opening brace)
    // We need to go back one position to capture the current token position (LBRACE)
    size_t block_start_pos = tokenizer_->get_current_pos() - 1;
    std::cout << "[DEBUG] Captured block_start_pos: " << block_start_pos << ", current token: " << current_token_.to_string() << std::endl;

    if (!condition) { // If initial condition is false, just consume the block once
        std::cout << "[DEBUG] Initial condition false, consuming block once" << std::endl;
        consume_block(); // Use consume_block instead of parse_block to skip execution
        return;
    }

    int iteration = 0;
    int max_iterations = 1000; // Safety limit to prevent infinite loops

    // Execute the first iteration
    std::cout << "[DEBUG] While loop iteration " << iteration << ", condition: " << condition << std::endl;
    parse_block();
    iteration++;

    // Continue with subsequent iterations
    while (!returned_ && iteration < max_iterations) {
        std::cout << "[DEBUG] Re-evaluating condition for iteration " << iteration << std::endl;

        try {
            // Reset to condition start and re-evaluate
            tokenizer_->set_current_pos(condition_start_pos);
            current_token_ = tokenizer_->get_next_token();
            std::cout << "[DEBUG] About to parse condition, current token: " << current_token_.to_string() << std::endl;
            condition_val = parse_boolean_expression();
            condition = condition_val.as_int();
            std::cout << "[DEBUG] New condition: " << condition << std::endl;

            if (!condition) {
                std::cout << "[DEBUG] Condition false, exiting while loop" << std::endl;
                break; // Exit the loop if condition is false
            }

            // Skip the closing parenthesis after condition
            eat(RPAREN);

            // Execute the block again
            std::cout << "[DEBUG] While loop iteration " << iteration << ", condition: " << condition << std::endl;
            tokenizer_->set_current_pos(block_start_pos);
            current_token_ = tokenizer_->get_next_token();
            std::cout << "[DEBUG] About to parse block, current token: " << current_token_.to_string() << std::endl;
            parse_block();
            std::cout << "[DEBUG] Block parsing completed" << std::endl;

            iteration++;
        } catch (const std::exception& e) {
            std::cout << "[DEBUG] Exception in while loop: " << e.what() << std::endl;
            throw; // Re-throw the exception
        }
    }

    if (iteration >= max_iterations) {
        std::cout << "[DEBUG] While loop hit maximum iteration limit" << std::endl;
    }

    // Position the tokenizer after the block for subsequent parsing
    tokenizer_->set_current_pos(block_start_pos);
    current_token_ = tokenizer_->get_next_token();
    consume_block(); // Skip to the end of the block

    std::cout << "[DEBUG] handle_while_statement() completed" << std::endl;
}

void Interpreter::parse_function_declaration() {
    std::cout << "[DEBUG] parse_function_declaration() called" << std::endl;
    eat(KEYWORD_FUNCTION);
    Token name_token = current_token_;
    std::cout << "[DEBUG] Function name token: " << name_token.to_string() << std::endl;
    eat(IDENTIFIER);
    std::string function_name = name_token.value;
    std::cout << "[DEBUG] Function name: " << function_name << std::endl;

    if (function_definitions_.count(function_name)) {
        throw std::runtime_error("Syntax Error: Function '" + function_name + "' already defined");
    }

    FunctionDefinition func_def;
    eat(LPAREN);
    while (current_token_.type == IDENTIFIER) {
        func_def.parameters.push_back(current_token_.value);
        eat(IDENTIFIER);
        if (current_token_.type == COMMA) {
            eat(COMMA);
        } else {
            break;
        }
    }
    eat(RPAREN);

    // Capture the position of the opening brace for the function body
    // We need to go back one position to capture the current token position (LBRACE)
    func_def.body_start_pos = tokenizer_->get_current_pos() - 1;
    std::cout << "[DEBUG] Captured function body start position: " << func_def.body_start_pos << ", current token: " << current_token_.to_string() << std::endl;

    // Consume the block to find its end position
    std::cout << "[DEBUG] About to consume function body block" << std::endl;
    consume_block();
    func_def.body_end_pos = tokenizer_->get_current_pos();
    std::cout << "[DEBUG] Function body consumed, end position: " << func_def.body_end_pos << std::endl;

    function_definitions_[function_name] = func_def;
    std::cout << "[DEBUG] Function definition stored for: " << function_name << std::endl;
}

Value Interpreter::parse_function_call(const std::string& function_name) {
    std::cout << "[DEBUG] parse_function_call() called for: " << function_name << std::endl;
    if (!function_definitions_.count(function_name)) {
        std::cout << "[DEBUG] Function not found: " << function_name << std::endl;
        throw std::runtime_error("Runtime Error: Undefined function '" + function_name + "'");
    }

    FunctionDefinition func_def = function_definitions_[function_name];
    std::cout << "[DEBUG] Function found, body start: " << func_def.body_start_pos << ", end: " << func_def.body_end_pos << std::endl;
    std::vector<Value> arguments;

    eat(LPAREN);
    while (current_token_.type != RPAREN) {
        arguments.push_back(parse_expression());
        if (current_token_.type == COMMA) {
            eat(COMMA);
        } else {
            break;
        }
    }
    eat(RPAREN);

    if (arguments.size() != func_def.parameters.size()) {
        throw std::runtime_error("Runtime Error: Function '" + function_name + "' expects " +
                                 std::to_string(func_def.parameters.size()) + " arguments, but got " +
                                 std::to_string(arguments.size()));
    }

    // Save current interpreter state AFTER parsing function call arguments
    size_t saved_tokenizer_pos = tokenizer_->get_current_pos();
    Token saved_current_token = current_token_;
    std::cout << "[DEBUG] Saved tokenizer position: " << saved_tokenizer_pos << ", saved token: " << saved_current_token.to_string() << std::endl;

    // Push new scope onto call stack
    call_stack_.push_back({});
    std::map<std::string, Value>& current_scope = call_stack_.back();

    // Bind arguments to parameters in the new scope
    for (size_t i = 0; i < func_def.parameters.size(); ++i) {
        current_scope[func_def.parameters[i]] = arguments[i];
    }

    // Execute function body
    std::cout << "[DEBUG] Setting tokenizer position to: " << func_def.body_start_pos << std::endl;
    tokenizer_->set_current_pos(func_def.body_start_pos);
    current_token_ = tokenizer_->get_next_token(); // Get first token of body
    std::cout << "[DEBUG] First token of function body: " << current_token_.to_string() << std::endl;
    returned_ = false; // Reset return flag for new function call
    return_value_ = Value(); // Reset return value

    std::cout << "[DEBUG] About to execute function body block" << std::endl;
    parse_block(); // Execute the block
    std::cout << "[DEBUG] Function body execution completed, returned: " << returned_ << std::endl;
    std::cout << "[DEBUG] Return value type: " << (return_value_.type == INT_TYPE ? "INT" : "STRING") << std::endl;

    // Pop scope from call stack
    call_stack_.pop_back();

    // Restore interpreter state
    std::cout << "[DEBUG] Restoring tokenizer position to: " << saved_tokenizer_pos << ", token: " << saved_current_token.to_string() << std::endl;
    tokenizer_->set_current_pos(saved_tokenizer_pos);
    current_token_ = saved_current_token;

    // Reset the returned flag to allow continued execution
    returned_ = false;

    std::cout << "[DEBUG] Function call completed, returning: " << return_value_.as_string() << std::endl;
    return return_value_; // Return the value (or void if no explicit return)
}

void Interpreter::handle_return_statement() {
    eat(KEYWORD_RETURN);
    if (current_token_.type != SEMICOLON) {
        return_value_ = parse_expression();
    }
    returned_ = true;
    // No eat(SEMICOLON) here, as parse_statement will eat it.
}

void Interpreter::consume_block() {
    eat(LBRACE);
    int brace_count = 1;
    while (brace_count > 0 && current_token_.type != END_OF_FILE) {
        if (current_token_.type == LBRACE) {
            brace_count++;
        } else if (current_token_.type == RBRACE) {
            brace_count--;
        }
        current_token_ = tokenizer_->get_next_token();
    }
    if (brace_count != 0) {
        throw std::runtime_error("Syntax Error: Unmatched braces in block consumption");
    }
}

void Interpreter::parse_statement() {
    if (returned_) {
        std::cout << "[DEBUG] Returned flag is true, skipping statement" << std::endl;
        return; // If a return statement was hit, stop processing statements in this block
    }

    if (current_token_.type == KEYWORD_LET) {
        eat(KEYWORD_LET);
        Token id_token = current_token_;
        eat(IDENTIFIER);
        eat(ASSIGN);
        set_variable(id_token.value, parse_expression());
        eat(SEMICOLON);
    } else if (current_token_.type == KEYWORD_PRINT) {
        handle_print_statement();
        eat(SEMICOLON);
    } else if (current_token_.type == KEYWORD_IF) {
        handle_if_statement();
    } else if (current_token_.type == KEYWORD_WHILE) {
        std::cout << "[DEBUG] Processing KEYWORD_WHILE" << std::endl;
        handle_while_statement();
    } else if (current_token_.type == KEYWORD_FUNCTION) {
        std::cout << "[DEBUG] Processing KEYWORD_FUNCTION" << std::endl;
        parse_function_declaration();
    } else if (current_token_.type == KEYWORD_RETURN) {
        std::cout << "[DEBUG] Processing KEYWORD_RETURN" << std::endl;
        handle_return_statement();
        eat(SEMICOLON);
    } else if (current_token_.type == IDENTIFIER) { // Could be a function call or variable assignment
        std::cout << "[DEBUG] Processing IDENTIFIER: " << current_token_.value << std::endl;
        Token id_token = current_token_;
        eat(IDENTIFIER);
        if (current_token_.type == LPAREN) {
            // Function call
            std::cout << "[DEBUG] IDENTIFIER followed by LPAREN - function call" << std::endl;
            parse_function_call(id_token.value); // Call function, ignore return value if not assigned
            eat(SEMICOLON);
        } else if (current_token_.type == ASSIGN) {
            // Variable assignment
            std::cout << "[DEBUG] IDENTIFIER followed by ASSIGN - variable assignment" << std::endl;
            eat(ASSIGN);
            set_variable(id_token.value, parse_boolean_expression());
            eat(SEMICOLON);
        } else {
            std::cout << "[DEBUG] IDENTIFIER not followed by LPAREN or ASSIGN, throwing error" << std::endl;
            throw std::runtime_error("Syntax Error: Unexpected identifier in statement: " + id_token.to_string());
        }
    } else {
        std::cout << "[DEBUG] Unexpected token, throwing error: " << current_token_.to_string() << std::endl;
        throw std::runtime_error("Syntax Error: Unexpected token in statement: " + current_token_.to_string());
    }
}

void Interpreter::parse_program() {
    while (current_token_.type != END_OF_FILE && !returned_) {
        parse_statement();
    }
}

std::string Interpreter::interpret(const std::string& code, std::function<void(const std::string&)> print_callback) {
    try {
        // Validate interpreter state
        if (!is_valid_state()) {
            handle_interpreter_error("Invalid interpreter state", "Cannot interpret code");
            return "Error: Interpreter not properly initialized\n";
        }

        // Validate input
        if (code.empty()) {
            logger_->warning("Empty code provided for interpretation");
            return "";
        }

        if (code.size() > 1000000) { // 1MB limit
            handle_interpreter_error("Code too large", "Size: " + std::to_string(code.size()));
            return "Error: Code size exceeds maximum limit\n";
        }

        logger_->info("Starting interpretation", "Interpreter", "interpret");
        log_interpreter_state("interpretation_start");

        Tokenizer tokenizer(code);
        if (!tokenizer.is_valid_state()) {
            handle_interpreter_error("Tokenizer initialization failed", "Invalid tokenizer state");
            return "Error: Failed to initialize tokenizer\n";
        }

        tokenizer_ = &tokenizer;
        current_token_ = tokenizer_->get_next_token();
        print_callback_ = print_callback;

        // Clear and re-initialize call stack for each interpretation
        call_stack_.clear();
        call_stack_.push_back({}); // Global scope
        function_definitions_.clear();
        returned_ = false;
        return_value_ = Value();
        current_recursion_depth_ = 0;

        parse_program();

        logger_->info("Interpretation completed successfully", "Interpreter", "interpret");
        return ""; // Output is now handled by the callback

    } catch (const utilities::BaseCustomException& e) {
        handle_interpreter_error("Custom exception during interpretation", e.getMessage());
        return "Error: " + e.getMessage() + "\n";
    } catch (const std::runtime_error& e) {
        handle_interpreter_error("Runtime error during interpretation", e.what());
        return "Error: " + std::string(e.what()) + "\n";
    } catch (const std::exception& e) {
        handle_interpreter_error("Unexpected exception during interpretation", e.what());
        return "Error: " + std::string(e.what()) + "\n";
    } catch (...) {
        handle_interpreter_error("Unknown exception during interpretation", "Caught unknown exception");
        return "Error: Unknown error occurred during interpretation\n";
    }
}

// Enhanced error handling and validation methods
bool Interpreter::is_valid_state() const {
    return initialized_ && !call_stack_.empty();
}

void Interpreter::reset_interpreter_state() {
    try {
        logger_->debug("Resetting interpreter state", "Interpreter", "reset_interpreter_state");
        call_stack_.clear();
        call_stack_.push_back({}); // Global scope
        function_definitions_.clear();
        returned_ = false;
        return_value_ = Value();
        current_recursion_depth_ = 0;
        tokenizer_ = nullptr;
        log_interpreter_state("reset");
    } catch (const std::exception& e) {
        handle_interpreter_error("Failed to reset interpreter state", e.what());
    }
}

void Interpreter::validate_interpreter_state() const {
    if (!initialized_) {
        throw utilities::ValidationError("Interpreter not properly initialized");
    }
    if (call_stack_.empty()) {
        throw utilities::ValidationError("Call stack is empty");
    }
    if (current_recursion_depth_ > max_call_stack_depth_) {
        throw utilities::ValidationError("Maximum recursion depth exceeded: " +
                                       std::to_string(current_recursion_depth_));
    }
}

void Interpreter::log_interpreter_state(const std::string& operation) const {
    if (!logger_) return;

    try {
        std::ostringstream oss;
        oss << "Interpreter state - Operation: " << operation
            << ", Call stack depth: " << call_stack_.size()
            << ", Recursion depth: " << current_recursion_depth_
            << ", Functions defined: " << function_definitions_.size()
            << ", Returned: " << (returned_ ? "true" : "false")
            << ", Initialized: " << (initialized_ ? "true" : "false");

        logger_->debug(oss.str(), "Interpreter", "log_interpreter_state");
    } catch (const std::exception& e) {
        // Avoid recursive error handling in logging
        std::cerr << "Failed to log interpreter state: " << e.what() << std::endl;
    }
}

void Interpreter::handle_interpreter_error(const std::string& error_msg, const std::string& context) const {
    try {
        if (logger_) {
            logger_->error("Interpreter error: " + error_msg +
                          (context.empty() ? "" : " - Context: " + context),
                          "Interpreter", "handle_interpreter_error");
        }

        if (error_handler_) {
            utilities::ProcessingError exception(error_msg, "INTERPRETER_ERROR");
            exception.setContext("Interpreter", "handle_interpreter_error", __LINE__);
            error_handler_->handleError(exception);
        }
    } catch (const std::exception& e) {
        // Fallback error handling
        std::cerr << "Critical interpreter error: " << error_msg << " - " << e.what() << std::endl;
    }
}

void Interpreter::validate_call_stack() const {
    if (call_stack_.empty()) {
        throw utilities::ValidationError("Call stack is empty");
    }
    if (call_stack_.size() > max_call_stack_depth_) {
        throw utilities::ValidationError("Call stack overflow: " + std::to_string(call_stack_.size()));
    }
}

void Interpreter::validate_tokenizer_state() const {
    if (!tokenizer_) {
        throw utilities::ValidationError("Tokenizer is null");
    }
    if (!tokenizer_->is_valid_state()) {
        throw utilities::ValidationError("Tokenizer is in invalid state");
    }
}

void Interpreter::safe_push_scope() {
    try {
        validate_call_stack();
        if (call_stack_.size() >= max_call_stack_depth_) {
            handle_interpreter_error("Cannot push scope", "Maximum call stack depth reached");
            return;
        }
        call_stack_.push_back({});
        logger_->debug("Pushed new scope", "Interpreter", "safe_push_scope",
                      static_cast<int>(call_stack_.size()));
    } catch (const std::exception& e) {
        handle_interpreter_error("Failed to push scope", e.what());
    }
}

void Interpreter::safe_pop_scope() {
    try {
        if (call_stack_.size() <= 1) {
            handle_interpreter_error("Cannot pop global scope", "Only global scope remains");
            return;
        }
        call_stack_.pop_back();
        logger_->debug("Popped scope", "Interpreter", "safe_pop_scope",
                      static_cast<int>(call_stack_.size()));
    } catch (const std::exception& e) {
        handle_interpreter_error("Failed to pop scope", e.what());
    }
}

bool Interpreter::is_safe_recursion_depth() const {
    return current_recursion_depth_ < max_call_stack_depth_;
}

void Interpreter::increment_recursion_depth() {
    current_recursion_depth_++;
    if (current_recursion_depth_ > max_call_stack_depth_) {
        handle_interpreter_error("Maximum recursion depth exceeded",
                               "Depth: " + std::to_string(current_recursion_depth_));
    }
}

void Interpreter::decrement_recursion_depth() {
    if (current_recursion_depth_ > 0) {
        current_recursion_depth_--;
    }
}

Value Interpreter::safe_parse_expression() {
    try {
        validate_interpreter_state();
        validate_tokenizer_state();
        return parse_expression();
    } catch (const std::exception& e) {
        handle_interpreter_error("Failed to parse expression", e.what());
        return Value(); // Return default value on error
    }
}

void Interpreter::safe_parse_statement() {
    try {
        validate_interpreter_state();
        validate_tokenizer_state();
        parse_statement();
    } catch (const std::exception& e) {
        handle_interpreter_error("Failed to parse statement", e.what());
    }
}